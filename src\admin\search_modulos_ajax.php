<?php

declare(strict_types=1);

use App\classes\ProyectoModulo;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

// Include necessary files
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check for valid database connection
if (!$conexion instanceof PDO) {
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']);
    exit;
}

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Método de solicitud no válido.");
    }

    // Get search parameters from POST
    $search_query = trim($_POST['query'] ?? '');
    $id_proyecto = filter_input(INPUT_POST, 'id_proyecto', FILTER_VALIDATE_INT);
    $limit = filter_input(INPUT_POST, 'limit', FILTER_VALIDATE_INT) ?: 10;

    // Validate required parameters
    if (empty($search_query) || strlen($search_query) < 2) {
        echo json_encode([
            'success' => true,
            'results' => [],
            'message' => 'Escriba al menos 2 caracteres para buscar.'
        ]);
        exit;
    }

    if (!$id_proyecto) {
        echo json_encode(['success' => false, 'message' => 'ID de proyecto requerido']);
        exit;
    }

    // Search modules for the project
    $modulos = ProyectoModulo::searchByProyecto($id_proyecto, $search_query, $limit, $conexion);

    // Format results for autocomplete
    $results = [];
    foreach ($modulos as $modulo) {
        $results[] = [
            'id' => $modulo->getId(),
            'descripcion' => $modulo->getDescripcion(),
            'display_text' => $modulo->getDescripcion()
        ];
    }

    echo json_encode(['success' => true, 'results' => $results]);

} catch (Exception $e) {
    error_log("Error in search_modulos_ajax.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al buscar módulos: ' . $e->getMessage()]);
}
